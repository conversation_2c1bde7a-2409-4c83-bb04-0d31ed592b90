<html>
  <head>
    <title>Chess Openings Trainer</title>

    <link rel="shortcut icon" href="/static/favicon.ico">

    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>

    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.bundle.min.js"></script>

    <script src="/static/js/chess.js"></script>

    <link rel="stylesheet" href="/static/css/chessboard-1.0.0.min.css">
    <script src="/static/js/chessboard-1.0.0.min.js"></script>

    <style>
      .trainer-controls {
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
      }
      .start-training-btn {
        font-size: 1.2rem;
        font-weight: bold;
        padding: 15px 30px;
        border-radius: 8px;
        width: 100%;
        margin-top: 20px;
      }
      .control-group {
        margin-bottom: 15px;
      }
      .control-label {
        font-weight: 600;
        margin-bottom: 5px;
        color: #495057;
      }
      .board-orientation-toggle {
        display: flex;
        align-items: center;
        gap: 10px;
      }
      .orientation-label {
        font-weight: 500;
        min-width: 60px;
      }
      .training-status {
        background-color: #e9ecef;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
        text-align: center;
      }
      .training-stats {
        display: flex;
        justify-content: space-around;
        margin-bottom: 15px;
      }
      .stat-item {
        text-align: center;
      }
      .stat-value {
        font-size: 1.5rem;
        font-weight: bold;
        color: #007bff;
      }
      .stat-label {
        font-size: 0.9rem;
        color: #6c757d;
      }
      .surrender-btn {
        background-color: #dc3545;
        border-color: #dc3545;
        color: white;
        font-weight: bold;
        padding: 10px 30px;
        border-radius: 6px;
      }
      .surrender-btn:hover {
        background-color: #c82333;
        border-color: #bd2130;
        color: white;
      }
      .hidden {
        display: none !important;
      }
    </style>

  </head>
  <body>
    <div class="container-fluid">
      <div class="row justify-content-center">
        <div class="col-12 col-lg-10 col-xl-8">
          <div class="card mt-3">
            <div class="card-header bg-primary text-white">
              <h3 class="mb-0 text-center">Chess Openings Trainer</h3>
            </div>
            <div class="card-body">
              <div class="row">
                <!-- Chess Board Column -->
                <div class="col-md-6">
                  <div id="chess_board" class="mx-auto mb-3" style="width: 400px;"></div>
                  <div class="text-center">
                    <strong><div id="status" class="mb-3"></div></strong>
                  </div>
                </div>

                <!-- Controls Column -->
                <div class="col-md-6">
                  <!-- Training Status (hidden by default) -->
                  <div id="training-status" class="training-status hidden">
                    <h5 class="mb-3 text-center">Training in Progress</h5>
                    <div class="training-stats">
                      <div class="stat-item">
                        <div id="remaining-time" class="stat-value">5:00</div>
                        <div class="stat-label">Time Remaining</div>
                      </div>
                      <div class="stat-item">
                        <div id="moves-passed" class="stat-value">0</div>
                        <div class="stat-label">Moves Passed</div>
                      </div>
                    </div>
                    <button id="surrender" class="btn surrender-btn">
                      🏳️ Surrender
                    </button>
                  </div>

                  <!-- Training Settings (visible by default) -->
                  <div id="training-controls" class="trainer-controls">
                    <h5 class="mb-3 text-center">Training Settings</h5>

                    <!-- Board Orientation Toggle -->
                    <div class="control-group">
                      <div class="control-label">Board Orientation</div>
                      <div class="board-orientation-toggle">
                        <span class="orientation-label">White</span>
                        <div class="custom-control custom-switch">
                          <input type="checkbox" class="custom-control-input" id="orientation-toggle">
                          <label class="custom-control-label" for="orientation-toggle"></label>
                        </div>
                        <span class="orientation-label">Black</span>
                      </div>
                    </div>

                    <!-- FEN Input -->
                    <div class="control-group">
                      <div class="control-label">Position (FEN)</div>
                      <div class="input-group">
                        <input id="fen" type="text" class="form-control" placeholder="Enter FEN or leave empty for starting position">
                        <div class="input-group-append">
                          <button id="set_fen" class="btn btn-outline-success">Set</button>
                        </div>
                      </div>
                    </div>

                    <!-- Move Time -->
                    <div class="control-group">
                      <div class="control-label">Move Time</div>
                      <select id="move_time" class="form-control">
                        <option value="instant" selected>Instant response</option>
                        <option value="1">1 second</option>
                        <option value="2">2 seconds</option>
                        <option value="3">3 seconds</option>
                        <option value="4">4 seconds</option>
                        <option value="5">5 seconds</option>
                        <option value="6">6 seconds</option>
                        <option value="7">7 seconds</option>
                        <option value="8">8 seconds</option>
                        <option value="9">9 seconds</option>
                        <option value="10">10 seconds</option>
                      </select>
                    </div>

                    <!-- Engine Selection -->
                    <div class="control-group">
                      <div class="control-label">Engine</div>
                      <select id="engine" class="form-control">
                        <option value="stockfish" selected>Stockfish</option>
                        <option value="lczero">Leela Chess Zero</option>
                      </select>
                    </div>

                    <!-- Start Training Button -->
                    <button id="start_training" class="btn btn-success start-training-btn">
                      🚀 Start Training
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </body>
</html>

<script>
  // Training state variables
  var trainingActive = false;
  var trainingParams = {};
  var trainingTimer = null;
  var remainingTime = 300; // 5 minutes in seconds
  var movesPassed = 0;

  // make computer move
  function make_move() {
    if (!trainingActive) return;

    // make HTTP POST request to make move API
    console.log('HERE', game.pgn());
    $.post('/make_move',{
        'pgn': game.pgn(),
        'move_time': trainingParams.moveTime,
        'engine_type': trainingParams.engine,
        'orientation': board.orientation()
      }, function(data) {
        // load fen into the current board state
        game.move(data.best_move, { sloppy: true })

        // update board position
        board.position(game.fen());

        // update game status
        updateStatus();

        // check if training should continue
        if (!data.continue) {
          endTraining('Training completed! You\'ve finished the opening sequence.');
        } else {
          // increment moves passed
          movesPassed++;
          updateTrainingStats();
        }
    }).fail(function() {
        endTraining('Error occurred during training.');
    });
  }

  // Update training statistics display
  function updateTrainingStats() {
    $('#moves-passed').text(movesPassed);

    var minutes = Math.floor(remainingTime / 60);
    var seconds = remainingTime % 60;
    $('#remaining-time').text(minutes + ':' + (seconds < 10 ? '0' : '') + seconds);
  }

  // Start training timer
  function startTrainingTimer() {
    trainingTimer = setInterval(function() {
      remainingTime--;
      updateTrainingStats();

      if (remainingTime <= 0) {
        endTraining('Time\'s up! Training session completed.');
      }
    }, 1000);
  }

  // End training session
  function endTraining(message) {
    trainingActive = false;

    if (trainingTimer) {
      clearInterval(trainingTimer);
      trainingTimer = null;
    }

    // Show training controls, hide training status
    $('#training-controls').removeClass('hidden');
    $('#training-status').addClass('hidden');

    // Show completion message
    alert(message);

    // Reset training variables
    remainingTime = 300;
    movesPassed = 0;
  }

  // handle orientation toggle
  $('#orientation-toggle').on('change', function() {
    if ($(this).is(':checked')) {
      // Playing as black - flip board to show black perspective
      board.orientation('black');
    } else {
      // Playing as white - show white perspective
      board.orientation('white');
    }
  });

  // handle start training button click
  $('#start_training').on('click', function() {
    // Store training parameters
    trainingParams = {
      orientation: $('#orientation-toggle').is(':checked') ? 'black' : 'white',
      moveTime: $('#move_time option:selected').val(),
      engine: $('#engine option:selected').val(),
      startingFen: $('#fen').val().trim()
    };

    // Reset game state
    game.reset();

    // Set board position based on FEN input or start position
    if (trainingParams.startingFen) {
      if (game.load(trainingParams.startingFen)) {
        board.position(game.fen());
      } else {
        alert('Invalid FEN! Using starting position.');
        game.reset();
        board.position('start');
        $('#fen').val('');
        trainingParams.startingFen = '';
      }
    } else {
      board.position('start');
    }

    // Set board orientation
    board.orientation(trainingParams.orientation);

    // Check if position is set up correctly for training
    var playerColor = trainingParams.orientation === 'white' ? 'w' : 'b';
    var currentTurn = game.turn();

    // Training should start when it's the engine's turn (opposite of player's color)
    if (currentTurn === playerColor) {
      alert('Position is set up incorrectly! It should be the engine\'s turn to move when training starts. Please adjust the position or choose a different orientation.');
      return;
    }

    // Reset training variables
    remainingTime = 300; // 5 minutes
    movesPassed = 0;
    trainingActive = true;

    // Hide training controls, show training status
    $('#training-controls').addClass('hidden');
    $('#training-status').removeClass('hidden');

    // Update training stats display
    updateTrainingStats();

    // Start the timer
    startTrainingTimer();

    // Update status
    updateStatus();

    // Make the first engine move to start the training
    setTimeout(function() {
      if (trainingActive && !game.game_over()) {
        make_move();
      }
    }, 500);
  });

  // handle surrender button click
  $('#surrender').on('click', function() {
    if (confirm('Are you sure you want to surrender?')) {
      endTraining('Training surrendered.');
    }
  });
  
  // handle set FEN button click
  $('#set_fen').on('click', function() {
    // Don't allow FEN changes during training
    if (trainingActive) {
      alert('Cannot change position during training!');
      return;
    }

    // set user FEN
    var fenInput = $('#fen').val().trim();

    // FEN parsed
    if (fenInput && game.load(fenInput)) {
      // set board position
      board.position(game.fen());
      updateStatus();
    }
    // FEN is not parsed
    else if (fenInput) {
      alert('Illegal FEN!');
    } else {
      // Empty FEN - reset to starting position
      game.reset();
      board.position('start');
      updateStatus();
    }
  });


  // GUI board & game state variables
  var board = null;
  var game = new Chess();
  var $status = $('#status');
  var $fen = $('#fen');
  var $pgn = $('#pgn');
  var $score = $('#score');
  var $depth = $('#depth');
  var $time = $('#time');
  var $nodes = $('#nodes');
  var $knps = $('#knps')

  // on picking up a piece
  function onDragStart (source, piece, position, orientation) {

    // only pick up pieces for the side to move
    if ((game.turn() === 'w' && piece.search(/^b/) !== -1) ||
        (game.turn() === 'b' && piece.search(/^w/) !== -1)) {
      return false
    }
  }

  // on dropping piece
  function onDrop (source, target) {
    // Only allow moves during training

    // see if the move is legal
    var move = game.move({
      from: source,
      to: target,
      promotion: 'q' // NOTE: always promote to a queen for example simplicity
    })

    // illegal move
    if (move === null) return 'snapback'

    // update game status
    updateStatus();

    // make computer move after a short delay
    setTimeout(function() {
      if (trainingActive && !game.game_over()) {
        make_move();
      }
    }, 300);
  }

  // update the board position after the piece snap
  // for castling, en passant, pawn promotion
  function onSnapEnd () {
    board.position(game.fen())
  }

  // update game status
  function updateStatus () {
    var status = ''

    var moveColor = 'White'
    if (game.turn() === 'b') {
      moveColor = 'Black'
    }

    // checkmate?
    if (game.in_checkmate()) {
      status = 'Game over, ' + moveColor + ' is in checkmate.'
    }

    // draw?
    else if (game.in_draw()) {
      status = 'Game over, drawn position'
    }

    // game still on
    else {
      status = moveColor + ' to move'

      // check?
      if (game.in_check()) {
        status += ', ' + moveColor + ' is in check'
      }
    }

    // update DOM elements
    $status.html(status)
    $fen.val(game.fen())
    $pgn.html(game.pgn())
  }

  // chess board configuration
  var config = {
    draggable: true,
    position: 'start',
    onDragStart: onDragStart,
    onDrop: onDrop,
    onSnapEnd: onSnapEnd
  }
  
  // create chess board widget instance
  board = Chessboard('chess_board', config)
  
  // prevent scrolling on touch devices
  $('#chess_board').on('scroll touchmove touchend touchstart contextmenu', function(e) {
    e.preventDefault();
  });

  // update game status
  updateStatus();
</script>
