<html>
  <head>
    <title>Chess Openings Trainer</title>

    <link rel="shortcut icon" href="/static/favicon.ico">

    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>

    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.bundle.min.js"></script>

    <script src="/static/js/chess.js"></script>

    <link rel="stylesheet" href="/static/css/chessboard-1.0.0.min.css">
    <script src="/static/js/chessboard-1.0.0.min.js"></script>

    <style>
      .trainer-controls {
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
      }
      .start-training-btn {
        font-size: 1.2rem;
        font-weight: bold;
        padding: 15px 30px;
        border-radius: 8px;
        width: 100%;
        margin-top: 20px;
      }
      .control-group {
        margin-bottom: 15px;
      }
      .control-label {
        font-weight: 600;
        margin-bottom: 5px;
        color: #495057;
      }
      .board-orientation-toggle {
        display: flex;
        align-items: center;
        gap: 10px;
      }
      .orientation-label {
        font-weight: 500;
        min-width: 60px;
      }
    </style>

  </head>
  <body>
    <div class="container-fluid">
      <div class="row justify-content-center">
        <div class="col-12 col-lg-10 col-xl-8">
          <div class="card mt-3">
            <div class="card-header bg-primary text-white">
              <h3 class="mb-0 text-center">Chess Openings Trainer</h3>
            </div>
            <div class="card-body">
              <div class="row">
                <!-- Chess Board Column -->
                <div class="col-md-6">
                  <div id="chess_board" class="mx-auto mb-3" style="width: 400px;"></div>
                  <div class="text-center">
                    <strong><div id="status" class="mb-3"></div></strong>
                  </div>
                </div>

                <!-- Controls Column -->
                <div class="col-md-6">
                  <div class="trainer-controls">
                    <h5 class="mb-3 text-center">Training Settings</h5>

                    <!-- Board Orientation Toggle -->
                    <div class="control-group">
                      <div class="control-label">Board Orientation</div>
                      <div class="board-orientation-toggle">
                        <span class="orientation-label">White</span>
                        <div class="custom-control custom-switch">
                          <input type="checkbox" class="custom-control-input" id="orientation-toggle">
                          <label class="custom-control-label" for="orientation-toggle"></label>
                        </div>
                        <span class="orientation-label">Black</span>
                      </div>
                    </div>

                    <!-- FEN Input -->
                    <div class="control-group">
                      <div class="control-label">Position (FEN)</div>
                      <div class="input-group">
                        <input id="fen" type="text" class="form-control" placeholder="Enter FEN or leave empty for starting position">
                        <div class="input-group-append">
                          <button id="set_fen" class="btn btn-outline-success">Set</button>
                        </div>
                      </div>
                    </div>

                    <!-- Move Time -->
                    <div class="control-group">
                      <div class="control-label">Move Time</div>
                      <select id="move_time" class="form-control">
                        <option value="instant" selected>Instant response</option>
                        <option value="1">1 second</option>
                        <option value="2">2 seconds</option>
                        <option value="3">3 seconds</option>
                        <option value="4">4 seconds</option>
                        <option value="5">5 seconds</option>
                        <option value="6">6 seconds</option>
                        <option value="7">7 seconds</option>
                        <option value="8">8 seconds</option>
                        <option value="9">9 seconds</option>
                        <option value="10">10 seconds</option>
                      </select>
                    </div>

                    <!-- Engine Selection -->
                    <div class="control-group">
                      <div class="control-label">Engine</div>
                      <select id="engine" class="form-control">
                        <option value="stockfish" selected>Stockfish</option>
                        <option value="lczero">Leela Chess Zero</option>
                      </select>
                    </div>

                    <!-- Start Training Button -->
                    <button id="start_training" class="btn btn-success start-training-btn">
                      🚀 Start Training
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </body>
</html>

<script>
  // make computer move
  function make_move() {
    // make HTTP POST request to make move API
    $.post('/make_move',{
        'pgn': game.pgn(),
        'move_time': $('#move_time option:selected').val(),
        'engine': $('#engine option:selected').val(),
        'orientation': board.orientation()
      }, function(data) {
        // load fen into the current board state
        game.move(data.best_move, { sloppy: true })

        // update board position
        board.position(game.fen());

        // update game status
        updateStatus();
    });
  }

  // handle orientation toggle
  $('#orientation-toggle').on('change', function() {
    if ($(this).is(':checked')) {
      // Playing as black - flip board to show black perspective
      board.orientation('black');
    } else {
      // Playing as white - show white perspective
      board.orientation('white');
    }
  });

  // handle start training button click
  $('#start_training').on('click', function() {
    // Reset game state
    game.reset();

    // Set board position based on FEN input or start position
    var fenInput = $('#fen').val().trim();
    if (fenInput) {
      if (game.load(fenInput)) {
        board.position(game.fen());
      } else {
        alert('Invalid FEN! Using starting position.');
        game.reset();
        board.position('start');
        $('#fen').val('');
      }
    } else {
      board.position('start');
    }

    // Update status
    updateStatus();

    // Show training started message
    alert('Training started! Make your moves and the engine will respond.');
  });
  
  // handle set FEN button click
  $('#set_fen').on('click', function() {
    // set user FEN
    
    // FEN parsed
    if (game.load($('#fen').val()))
      // set board position
      board.position(game.fen());
    
    // FEN is not parsed
    else
      alert('Illegal FEN!');
  });


  // GUI board & game state variables
  var board = null;
  var game = new Chess();
  var $status = $('#status');
  var $fen = $('#fen');
  var $pgn = $('#pgn');
  var $score = $('#score');
  var $depth = $('#depth');
  var $time = $('#time');
  var $nodes = $('#nodes');
  var $knps = $('#knps')

  // on picking up a piece
  function onDragStart (source, piece, position, orientation) {
    // do not pick up pieces if the game is over
    if (game.game_over()) return false

    // only pick up pieces for the side to move
    if ((game.turn() === 'w' && piece.search(/^b/) !== -1) ||
        (game.turn() === 'b' && piece.search(/^w/) !== -1)) {
      return false
    }
  }

  // on dropping piece
  function onDrop (source, target) {
    // see if the move is legal
    var move = game.move({
      from: source,
      to: target,
      promotion: 'q' // NOTE: always promote to a queen for example simplicity
    })

    // illegal move
    if (move === null) return 'snapback'

    // update game status
    updateStatus();

    // make computer move after a short delay
    // make_move();
  }

  // update the board position after the piece snap
  // for castling, en passant, pawn promotion
  function onSnapEnd () {
    board.position(game.fen())
  }

  // update game status
  function updateStatus () {
    var status = ''

    var moveColor = 'White'
    if (game.turn() === 'b') {
      moveColor = 'Black'
    }

    // checkmate?
    if (game.in_checkmate()) {
      status = 'Game over, ' + moveColor + ' is in checkmate.'
    }

    // draw?
    else if (game.in_draw()) {
      status = 'Game over, drawn position'
    }

    // game still on
    else {
      status = moveColor + ' to move'

      // check?
      if (game.in_check()) {
        status += ', ' + moveColor + ' is in check'
      }
    }

    // update DOM elements
    $status.html(status)
    $fen.val(game.fen())
    $pgn.html(game.pgn())
  }

  // chess board configuration
  var config = {
    draggable: true,
    position: 'start',
    onDragStart: onDragStart,
    onDrop: onDrop,
    onSnapEnd: onSnapEnd
  }
  
  // create chess board widget instance
  board = Chessboard('chess_board', config)
  
  // prevent scrolling on touch devices
  $('#chess_board').on('scroll touchmove touchend touchstart contextmenu', function(e) {
    e.preventDefault();
  });

  // update game status
  updateStatus();
</script>
